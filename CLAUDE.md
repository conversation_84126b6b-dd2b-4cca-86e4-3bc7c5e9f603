# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Quick Commands

### Development
- `pnpm dev` - Start dev server with hot reload
- `pnpm build` - Build for production
- `pnpm preview` - Browser preview during development
- `pnpm release` - Create release build

### Package Management
- Use `pnpm` as the package manager (version 10.14.0 configured in package.json)
- Run `pnpm install` to install dependencies

### Testing & Linting
- Check for TypeScript errors with `pnpm typecheck` (runs `tsc --noEmit`)
- **Biome** is used for both formatting and linting (replaces ESLint)
  - `pnpm format` - Format code with Biome
  - `pnpm format:check` - Check formatting without changes
  - `pnpm lint` - Run Biome linter
  - `pnpm lint:fix` - Fix linting issues automatically
  - `pnpm check` - Run both formatting and linting with auto-fix
- No specific test framework is configured

## Architecture Overview

### Figma Plugin Structure
This is a **Plugma** framework plugin with React frontend and TypeScript backend.

**Key Files:**
- `src/main.ts` - Main plugin script that runs in Figma's sandbox
- `src/ui.tsx` - React UI entry point
- `src/router/AppRouter.tsx` - React Router configuration with routes for Home, Login, About, Dashboard, Search, Plug detail, and 404
- `src/store/useAppStore.ts` - Zustand store with custom Figma storage adapter

### State Management
- **Zustand** store in `src/store/useAppStore.ts` with persistence via custom Figma storage adapter
- Custom Figma storage adapter communicates with main thread via postMessage API
- State includes user info, settings (theme/language), loading states, and auth token
- Storage operations use async message passing with timeout handling (5s)

### Main Thread Communication
The plugin uses postMessage for UI-main thread communication:
- Storage operations (get/set/delete) via `figma.clientStorage` with async response handling
- Selection change events tracked via `figma.on('selectionchange')` and posted as `POST_NODE_COUNT`
- Figma login token handling with `figma-login` and `figma-login-response` messages
- All messages use the format `{pluginMessage: {type, ...data}}`

### UI Components
- **shadcn/ui** components integrated (New York style) in `src/components/ui/`
- **Radix UI** components (Avatar, Button, Card, Checkbox, Dialog, Input, Label, Popover, ScrollArea, Select, Skeleton, Slot, Tooltip)
- **Tailwind CSS** for styling (version 4.x with Vite plugin) with **tailwind-merge** for class merging
- **Lucide React** for icons
- **Sonner** for toast notifications
- **Next Themes** for theme management
- **class-variance-authority** and **clsx** for conditional styling
- **tw-animate-css** for additional animations
- Custom Figma theme integration using CSS variables for light/dark mode support

### Navigation
- React Router (v7) with BrowserRouter
- Routes: `/` (HomePage), `/login` (LoginPage), `/about` (AboutPage), `/search` (SearchPage), `/dashboard` (DashboardPage), `/plug/:id` (PlugPage), `*` (NotFoundPage with 404)
- 404 page includes Chinese text ("页面未找到", "返回首页")
- Path alias `@/*` maps to `./src/*` (configured in vite.config.js and tsconfig.json)

### Plugin Configuration
- Manifest configured in `package.json` under `plugma` field with pluginVersion: "1"
- Network access allowed for `https://plugmate.figmod.top` in production
- Development allows `localhost` domains and `ws://localhost:9001` for hot reload
- Plugin name: "PlugMate" with ID "plugmate-replace"
- Supports both Figma and FigJam editors
- Plugin dimensions: 400x870px (no theme colors enabled)

### Development Workflow
1. `pnpm dev` - Start development server
2. Import `dist/manifest.json` in Figma desktop app
3. Use `pnpm preview` for browser testing
4. `pnpm build` before publishing

### TypeScript Configuration
- Strict mode enabled with comprehensive linting rules (noUnusedLocals, noUnusedParameters, noFallthroughCasesInSwitch)
- ES2020 target with ESNext module system and bundler resolution
- React JSX transform enabled
- Figma plugin typings included via `@figma/plugin-typings`
- Custom type roots: `node_modules/@figma` and `node_modules/@types`

### Key Dependencies
- **Plugma** (v1.2.10) - Main framework for Figma plugin development
- **React** (v18.3.1) with React Router v7 for navigation
- **Zustand** (v5.0.8) for state management
- **Vite** (v5.4.19) as the build tool with React plugin

## Component Architecture
- Pages follow a consistent layout pattern using `HeaderBox` component for navigation
- Reusable components: `Search` (search functionality), `Plug` (plugin card display), `HomeTabs` (tab navigation with emoji icons)
- Individual plugin detail pages via `PlugPage` component with favorites functionality
- Components use Tailwind CSS classes with responsive design patterns and shadcn/ui integration
- All components use TypeScript with proper type definitions
- Chinese language support throughout the UI (interface is primarily in Chinese)

### Code Style & Tooling
- **Biome** configuration enforces consistent code style:
  - 2-space indentation, 100 character line width
  - Single quotes for strings, semicolons required
  - PascalCase for classes/interfaces/types, camelCase for functions
  - Custom globals for Figma plugin environment (`figma`, `__html__`)
- **Tailwind CSS** with custom theme integration using Figma's CSS variables
- **Path aliases**: `@/*` maps to `./src/*` for clean imports