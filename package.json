{"private": true, "type": "module", "version": "1.0.1", "scripts": {"dev": "plugma dev", "build": "plugma build", "preview": "plugma preview", "release": "plugma release", "format": "biome format --write", "format:check": "biome format", "lint": "biome lint", "lint:fix": "biome lint --apply", "check": "biome check --write", "typecheck": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.1.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "ky": "^1.10.0", "lucide-react": "^0.511.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router": "^7.8.2", "tailwind-merge": "^3.3.1", "zustand": "^5.0.8"}, "devDependencies": {"@biomejs/biome": "^2.2.2", "@figma/plugin-typings": "^1.117.0", "@types/node": "^22.18.0", "@types/react": "^18.3.24", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.7.0", "plugma": "^1.2.10", "tailwindcss": "^4.1.12", "typescript": "~5.6.3", "vite": "^5.4.19"}, "plugma": {"manifest": {"name": "PlugMate", "id": "plugmate-replace", "api": "1.0.0", "main": "src/main.ts", "ui": "src/ui.tsx", "editorType": ["figma", "figjam"], "networkAccess": {"allowedDomains": ["https://plugmate.figmod.top"], "devAllowedDomains": ["http://localhost:*", "ws://localhost:9001"]}}, "pluginVersion": "1"}, "packageManager": "pnpm@10.14.0+sha512.ad27a79641b49c3e481a16a805baa71817a04bbe06a38d17e60e2eaee83f6a146c6a688125f5792e48dd5ba30e7da52a5cda4c3992b9ccf333f9ce223af84748"}