{"$schema": "https://biomejs.dev/schemas/2.2.2/schema.json", "files": {"ignoreUnknown": true, "includes": ["**/src/**/*.{ts,tsx,js,jsx,json}"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100, "lineEnding": "lf", "bracketSpacing": true, "bracketSameLine": false, "attributePosition": "auto"}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"noSvgWithoutTitle": "off"}, "correctness": {"noConstantCondition": "off"}, "style": {"useNamingConvention": {"level": "warn", "options": {"requireAscii": false, "conventions": [{"selector": {"kind": "function"}, "formats": ["camelCase", "PascalCase"]}, {"selector": {"kind": "class"}, "formats": ["PascalCase"]}, {"selector": {"kind": "interface"}, "formats": ["PascalCase"]}, {"selector": {"kind": "typeAlias"}, "formats": ["PascalCase"]}, {"selector": {"kind": "enum"}, "formats": ["PascalCase"]}]}}}}}, "javascript": {"globals": ["React", "figma", "console", "window", "document", "process"], "formatter": {"quoteStyle": "single", "jsxQuoteStyle": "single", "semicolons": "always", "arrowParentheses": "always", "bracketSameLine": false, "bracketSpacing": true}}, "json": {"parser": {"allowComments": true}}, "overrides": [{"includes": ["**/*.config.{js,ts}"], "javascript": {"globals": ["module", "require", "__dirname", "process", "global"]}}, {"includes": ["**/src/main.ts"], "javascript": {"globals": ["figma", "__html__", "require"]}}]}