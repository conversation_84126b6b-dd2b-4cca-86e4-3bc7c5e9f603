// Read the docs https://plugma.dev/docs

const getStorage = async (key: string) => {
  const value = await figma.clientStorage.getAsync(key);
  return value;
};

const setStorage = async (key: string, value: unknown) => {
  await figma.clientStorage.setAsync(key, value);
};

const deleteStorage = async (key: string) => {
  await figma.clientStorage.deleteAsync(key);
};

// OAuth 相关消息处理
const handleOAuthLogin = () => {
  figma.ui.postMessage({
    type: 'oauth-login-request',
    success: true,
  });
};

const handleOAuthCallback = async (code: string, state: string) => {
  try {
    // 将 OAuth 回调信息传递给 UI 处理
    figma.ui.postMessage({
      type: 'oauth-callback',
      code,
      state,
      success: true,
    });
  } catch (error) {
    console.error('OAuth callback handling error:', error);
    figma.ui.postMessage({
      type: 'oauth-callback',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

const handleOAuthSuccess = (userData: any) => {
  // 处理 OAuth 登录成功
  figma.ui.postMessage({
    type: 'oauth-success',
    user: userData,
    success: true,
  });
};

const handleOAuthError = (error: string) => {
  // 处理 OAuth 登录错误
  figma.ui.postMessage({
    type: 'oauth-error',
    error,
    success: false,
  });
};

const handleOpenExternalUrl = (url: string) => {
  // 在外部浏览器中打开 URL
  figma.ui.postMessage({
    type: 'open-external-url',
    url,
    success: true,
  });
};

export default function () {
  // figma.showUI(__html__, { width: 400, height: 870, themeColors: true })
  figma.showUI(__html__, { width: 400, height: 870 });

  figma.ui.onmessage = async (message) => {
    // 处理存储相关的消息
    if (message.type === 'storage-get') {
      try {
        const value = await getStorage(message.key);
        figma.ui.postMessage({
          type: 'storage-get-response',
          key: message.key,
          value: value,
        });
      } catch (error) {
        console.error('Error getting from clientStorage:', error);
        figma.ui.postMessage({
          type: 'storage-get-response',
          key: message.key,
          value: null,
        });
      }
    }

    if (message.type === 'storage-set') {
      try {
        await setStorage(message.key, message.value);
      } catch (error) {
        console.error('Error setting to clientStorage:', error);
      }
    }

    if (message.type === 'storage-delete') {
      try {
        await deleteStorage(message.key);
      } catch (error) {
        console.error('Error deleting from clientStorage:', error);
      }
    }

    // 处理 OAuth 相关的消息
    if (message.type === 'oauth-login') {
      handleOAuthLogin();
    }

    if (message.type === 'oauth-callback') {
      await handleOAuthCallback(message.code, message.state);
    }

    if (message.type === 'oauth-success') {
      handleOAuthSuccess(message.user);
    }

    if (message.type === 'oauth-error') {
      handleOAuthError(message.error);
    }

    if (message.type === 'open-external-url') {
      handleOpenExternalUrl(message.url);
    }

    // 兼容旧的 figma-login 消息
    if (message.type === 'figma-login') {
      figma.ui.postMessage({
        type: 'figma-login-response',
        token: message.token,
      });
    }
  };

  function postNodeCount() {
    const nodeCount = figma.currentPage.selection.length;

    figma.ui.postMessage({
      type: 'POST_NODE_COUNT',
      count: nodeCount,
    });
  }

  figma.on('selectionchange', postNodeCount);
}
