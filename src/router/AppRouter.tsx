import { BrowserRouter, Route, Routes } from 'react-router';
import { AdminRoute, AuthRoute, MemberRoute } from '@/components/Auth/ProtectedRoute';
import LoginPage from '@/pages/LoginPage';
import PlugPage from '@/pages/PlugPage';
import SearchPage from '@/pages/SearchPage';
import AboutPage from '../pages/AboutPage';
import DashboardPage from '../pages/DashboardPage';
import HomePage from '../pages/HomePage';

export default function AppRouter() {
  return (
    <BrowserRouter>
      <Routes>
        {/* 公开路由 */}
        <Route path='/' element={<HomePage />} />
        <Route path='/login' element={<LoginPage />} />
        <Route path='/about' element={<AboutPage />} />

        {/* 需要登录的路由 */}
        <Route
          path='/search'
          element={
            <AuthRoute>
              <SearchPage />
            </AuthRoute>
          }
        />

        {/* 需要会员权限的路由 */}
        <Route
          path='/plug/:id'
          element={
            <MemberRoute>
              <PlugPage />
            </MemberRoute>
          }
        />

        {/* 需要管理员权限的路由 */}
        <Route
          path='/dashboard'
          element={
            <AdminRoute>
              <DashboardPage />
            </AdminRoute>
          }
        />

        {/* 404 页面 */}
        <Route path='*' element={<NotFoundPage />} />
      </Routes>
    </BrowserRouter>
  );
}

// 404 页面组件
function NotFoundPage() {
  return (
    <div className='min-h-screen flex items-center justify-center p-8'>
      <div className='text-center'>
        <h1 className='text-6xl font-bold text-gray-900 dark:text-white mb-4'>404</h1>
        <p className='text-xl text-gray-600 dark:text-gray-300 mb-6'>页面未找到</p>
        <a
          href='/'
          className='px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors'
        >
          返回首页
        </a>
      </div>
    </div>
  );
}
