import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import type { User } from '../utils/api';

// 创建消息传递的存储适配器
const figmaStorage = {
  getItem: async (name: string): Promise<string | null> => {
    try {
      return new Promise((resolve) => {
        // 发送消息到 main.ts 获取数据
        parent.postMessage(
          {
            pluginMessage: {
              type: 'storage-get',
              key: name,
            },
          },
          '*',
        );

        // 监听来自 main.ts 的响应
        const handleMessage = (event: MessageEvent) => {
          if (
            event.data.pluginMessage?.type === 'storage-get-response' &&
            event.data.pluginMessage?.key === name
          ) {
            window.removeEventListener('message', handleMessage);
            const value = event.data.pluginMessage.value;
            resolve(value ? JSON.stringify(value) : null);
          }
        };

        window.addEventListener('message', handleMessage);

        // 设置超时以防止无限等待
        setTimeout(() => {
          window.removeEventListener('message', handleMessage);
          resolve(null);
        }, 5000);
      });
    } catch (error) {
      console.error('Error getting item from figma clientStorage:', error);
      return null;
    }
  },

  setItem: async (name: string, value: string): Promise<void> => {
    try {
      const parsedValue = JSON.parse(value);
      parent.postMessage(
        {
          pluginMessage: {
            type: 'storage-set',
            key: name,
            value: parsedValue,
          },
        },
        '*',
      );
    } catch (error) {
      console.error('Error setting item to figma clientStorage:', error);
    }
  },

  removeItem: async (name: string): Promise<void> => {
    try {
      parent.postMessage(
        {
          pluginMessage: {
            type: 'storage-delete',
            key: name,
          },
        },
        '*',
      );
    } catch (error) {
      console.error('Error removing item from figma clientStorage:', error);
    }
  },
};

// 定义认证状态接口
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  authError: string | null;
  isAuthLoading: boolean;
  lastAuthCheck: number | null;
}

// 定义应用状态接口
interface AppState {
  // 认证状态
  auth: AuthState;

  // 应用设置
  settings: {
    theme: 'light' | 'dark';
    language: 'en' | 'zh';
  };

  // 应用加载状态
  isLoading: boolean;

  // 认证相关 Actions
  setAuthenticated: (isAuthenticated: boolean) => void;
  setUser: (user: User | null) => void;
  setAuthError: (error: string | null) => void;
  setAuthLoading: (loading: boolean) => void;
  setLastAuthCheck: (timestamp: number) => void;
  clearAuth: () => void;

  // 应用设置 Actions
  updateSettings: (settings: Partial<AppState['settings']>) => void;

  // 应用状态 Actions
  setLoading: (loading: boolean) => void;
}

// 创建 store
export const useAppStore = create<AppState>()(
  persist(
    (set, _get) => ({
      // 初始认证状态
      auth: {
        isAuthenticated: false,
        user: null,
        authError: null,
        isAuthLoading: false,
        lastAuthCheck: null,
      },

      // 初始设置
      settings: {
        theme: 'light',
        language: 'zh',
      },

      // 初始加载状态
      isLoading: false,

      // 认证相关 Actions
      setAuthenticated: (isAuthenticated) =>
        set((state) => ({
          auth: { ...state.auth, isAuthenticated },
        })),

      setUser: (user) =>
        set((state) => ({
          auth: { ...state.auth, user, isAuthenticated: !!user },
        })),

      setAuthError: (authError) =>
        set((state) => ({
          auth: { ...state.auth, authError },
        })),

      setAuthLoading: (isAuthLoading) =>
        set((state) => ({
          auth: { ...state.auth, isAuthLoading },
        })),

      setLastAuthCheck: (lastAuthCheck) =>
        set((state) => ({
          auth: { ...state.auth, lastAuthCheck },
        })),

      clearAuth: () =>
        set(() => ({
          auth: {
            isAuthenticated: false,
            user: null,
            authError: null,
            isAuthLoading: false,
            lastAuthCheck: null,
          },
        })),

      // 应用设置 Actions
      updateSettings: (newSettings) =>
        set((state) => ({
          settings: { ...state.settings, ...newSettings },
        })),

      // 应用状态 Actions
      setLoading: (isLoading) => set({ isLoading }),
    }),
    {
      name: 'app-store',
      storage: createJSONStorage(() => figmaStorage),
      partialize: (state) => ({
        auth: {
          isAuthenticated: state.auth.isAuthenticated,
          user: state.auth.user,
          lastAuthCheck: state.auth.lastAuthCheck,
        },
        settings: state.settings,
      }),
    },
  ),
);

// 认证相关的选择器
export const useAuth = () => {
  const auth = useAppStore((state) => state.auth);
  const setAuthenticated = useAppStore((state) => state.setAuthenticated);
  const setUser = useAppStore((state) => state.setUser);
  const setAuthError = useAppStore((state) => state.setAuthError);
  const setAuthLoading = useAppStore((state) => state.setAuthLoading);
  const setLastAuthCheck = useAppStore((state) => state.setLastAuthCheck);
  const clearAuth = useAppStore((state) => state.clearAuth);

  return {
    // 状态
    ...auth,

    // Actions
    setAuthenticated,
    setUser,
    setAuthError,
    setAuthLoading,
    setLastAuthCheck,
    clearAuth,

    // 衍生状态
    isAdmin: auth.user?.role === 'admin',
    isMember: auth.user?.isMember === 1 || auth.user?.role === 'admin',
    displayName: auth.user?.name || auth.user?.email || 'Unknown User',
    avatarUrl: auth.user?.figmaImageUrl,
  };
};

// 应用设置相关的选择器
export const useSettings = () => {
  const settings = useAppStore((state) => state.settings);
  const updateSettings = useAppStore((state) => state.updateSettings);

  return {
    settings,
    updateSettings,
  };
};

// 应用状态相关的选择器
export const useAppLoading = () => {
  return useAppStore((state) => state.isLoading);
};
