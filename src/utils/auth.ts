import { type User, userApi } from './api';

// 认证状态类型
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  error: string | null;
}

// 登录 URL 配置
const LOGIN_URL = 'https://plugmate.figmod.top/login';
const REDIRECT_URI = `${window.location.origin}/auth/callback`;

// 生成随机状态令牌用于 CSRF 保护
export const generateStateToken = (): string => {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
};

// 存储状态令牌到 sessionStorage
export const saveStateToken = (token: string): void => {
  sessionStorage.setItem('oauth_state_token', token);
};

// 获取存储的状态令牌
export const getStateToken = (): string | null => {
  return sessionStorage.getItem('oauth_state_token');
};

// 清除状态令牌
export const clearStateToken = (): void => {
  sessionStorage.removeItem('oauth_state_token');
};

// 启动 OAuth 登录流程
export const initiateOAuthLogin = (): void => {
  const stateToken = generateStateToken();
  saveStateToken(stateToken);

  // 构建 OAuth 登录 URL
  const loginUrl = new URL(LOGIN_URL);
  loginUrl.searchParams.append('redirect_uri', REDIRECT_URI);
  loginUrl.searchParams.append('state', stateToken);

  // 在新窗口中打开登录页面
  window.open(loginUrl.toString(), '_blank');
};

// 处理 OAuth 回调
export const handleOAuthCallback = async (_code: string, state: string): Promise<User> => {
  // 验证状态令牌
  const savedState = getStateToken();
  if (!savedState || savedState !== state) {
    throw new Error('Invalid state token');
  }

  clearStateToken();

  try {
    // 验证会话并获取用户信息
    const response = await userApi.validateSession();
    if (response.valid && response.user) {
      return response.user;
    } else {
      throw new Error('Session validation failed');
    }
  } catch (error) {
    console.error('OAuth callback error:', error);
    throw error;
  }
};

// 验证当前会话
export const validateCurrentSession = async (): Promise<User | null> => {
  try {
    const response = await userApi.validateSession();
    return response.valid ? response.user || null : null;
  } catch (error) {
    console.error('Session validation error:', error);
    return null;
  }
};

// 用户登出
export const logoutUser = async (): Promise<void> => {
  try {
    await userApi.logout();
  } catch (error) {
    console.error('Logout error:', error);
    // 即使 API 调用失败，也要清除本地状态
  } finally {
    // 清除本地状态
    clearStateToken();
  }
};

// 检查用户权限
export const hasPermission = (user: User | null, permission: 'admin' | 'member'): boolean => {
  if (!user) return false;

  if (permission === 'admin') {
    return user.role === 'admin';
  }

  if (permission === 'member') {
    return user.isMember === 1 || user.role === 'admin';
  }

  return false;
};

// 格式化用户显示名称
export const formatUserDisplayName = (user: User | null): string => {
  if (!user) return '';
  return user.name || user.email || 'Unknown User';
};

// 获取用户头像 URL
export const getUserAvatarUrl = (user: User | null): string | null => {
  return user?.figmaImageUrl || null;
};

// 检查是否为新用户
export const isNewUser = (user: User | null): boolean => {
  if (!user) return false;

  // 简单判断：创建时间在 24 小时内的用户
  const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000;
  return user.createdAt * 1000 > oneDayAgo;
};

// 认证错误类型
export enum AuthErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  AUTH_ERROR = 'AUTH_ERROR',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  INVALID_STATE = 'INVALID_STATE',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

// 认证错误处理
export const handleAuthError = (error: any): { type: AuthErrorType; message: string } => {
  console.error('Authentication error:', error);

  if (error.name === 'NetworkError' || error.message?.includes('network')) {
    return {
      type: AuthErrorType.NETWORK_ERROR,
      message: '网络连接失败，请检查网络设置',
    };
  }

  if (error.response?.status === 401) {
    return {
      type: AuthErrorType.SESSION_EXPIRED,
      message: '登录已过期，请重新登录',
    };
  }

  if (error.message?.includes('Invalid state token')) {
    return {
      type: AuthErrorType.INVALID_STATE,
      message: '安全验证失败，请重新登录',
    };
  }

  return {
    type: AuthErrorType.UNKNOWN_ERROR,
    message: error.message || '认证失败，请重试',
  };
};

// 监听来自 OAuth 窗口的消息
export const setupOAuthMessageListener = (callback: (user: User) => void): (() => void) => {
  const handleMessage = (event: MessageEvent) => {
    // 验证消息来源
    if (event.origin !== new URL(LOGIN_URL).origin) {
      return;
    }

    // 验证消息格式
    if (event.data?.type === 'oauth_success' && event.data?.user) {
      callback(event.data.user);
    }
  };

  window.addEventListener('message', handleMessage);

  // 返回清理函数
  return () => {
    window.removeEventListener('message', handleMessage);
  };
};
