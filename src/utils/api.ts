import ky from 'ky';

// API 配置
const API_BASE_URL = 'https://plugmate.figmod.top';

// 创建 API 客户端实例
export const api = ky.create({
  prefixUrl: API_BASE_URL,
  timeout: 30000,
  retry: {
    limit: 2,
    methods: ['get', 'post'],
    statusCodes: [408, 413, 429, 500, 502, 503, 504],
  },
  hooks: {
    beforeRequest: [
      (request) => {
        // 自动包含 cookie 用于会话认证（使用 credentials 选项）
        (request as any).credentials = 'include';
      },
    ],
    afterResponse: [
      async (_request, _options, response) => {
        if (response.status === 401) {
          // 未授权，清除本地状态并重定向到登录页
          console.error('Authentication failed, redirecting to login');
          // 这里可以触发登出逻辑
        }
      },
    ],
  },
});

// 用户相关 API
export const userApi = {
  // 获取当前用户信息
  getCurrentUser: () => api.get('api/user').json<User>(),

  // 验证会话状态
  validateSession: () => api.get('api/validate-session').json<{ valid: boolean; user?: User }>(),

  // 用户登出
  logout: () => api.get('api/logout'),
};

// 插件相关 API
export const pluginApi = {
  // 获取插件列表
  getPlugins: (params?: { page?: number; limit?: number; search?: string }) =>
    api.get('api/plugins', { searchParams: params }).json<PluginResponse>(),

  // 获取插件详情
  getPluginById: (id: string) => api.get(`api/plugins/${id}`).json<Plugin>(),

  // 搜索插件
  searchPlugins: (query: string, params?: { page?: number; limit?: number }) =>
    api.get('api/plugins/search', { searchParams: { q: query, ...params } }).json<PluginResponse>(),
};

// 类型定义
export interface User {
  id: number;
  name: string;
  email: string;
  figmaId: string;
  figmaImageUrl?: string;
  role: 'admin' | 'user';
  isMember: number;
  searchCount: number;
  createdAt: number;
  updatedAt: number;
}

export interface Plugin {
  id: number;
  name: string;
  description: string;
  author: string;
  category: string;
  imageUrl?: string;
  downloadUrl?: string;
  figmaUrl?: string;
  tags: string[];
  isFeatured: boolean;
  downloadCount: number;
  createdAt: number;
  updatedAt: number;
}

export interface PluginResponse {
  plugins: Plugin[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// 通用响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 错误处理工具
export const handleApiError = (error: any): string => {
  if (error.response) {
    // API 返回的错误
    return error.data?.error || error.data?.message || 'API 请求失败';
  } else if (error.request) {
    // 网络错误
    return '网络连接失败，请检查网络设置';
  } else {
    // 其他错误
    return error.message || '未知错误';
  }
};
