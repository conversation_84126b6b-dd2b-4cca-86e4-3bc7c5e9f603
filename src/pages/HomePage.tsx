// import { useAppStore } from '../store/useAppStore';
import HeaderBox from '@/components/header';
import HomeTabs from '@/components/home-tabs';
import Plug from '@/components/plug';

export default function HomePage() {
  // const { user, settings, setUser, updateSettings } = useAppStore();

  return (
    <div className='relative h-screen flex flex-col'>
      <HeaderBox />
      <div className='flex-1 flex flex-col p-0 gap-2.5 relative overflow-hidden'>
        <HomeTabs />
        <div className='flex-1 w-full h-full relative overflow-y-auto'>
          <div className='space-y-3 pb-10 px-3'>
            <Plug />
            <Plug />
            <Plug />
          </div>
        </div>
      </div>
    </div>
  );
}
