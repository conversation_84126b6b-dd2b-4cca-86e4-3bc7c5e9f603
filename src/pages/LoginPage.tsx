import { AlertCircle, ExternalLink, Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { NavLink, useNavigate } from 'react-router';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';
import LogoUrl from '../assets/logo.png';

export default function LoginPage() {
  const { login, isLoading, error, isAuthenticated, isOAuthWindowOpen, clearError } = useAuth();
  const navigate = useNavigate();

  // 如果已经登录，重定向到首页
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);

  // 清除错误信息
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        clearError();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  const handleFigmaLogin = async () => {
    try {
      await login();
    } catch (error) {
      console.error('Login error:', error);
    }
  };

  return (
    <div className='relative h-screen flex flex-col'>
      <div className='border-b-[0.5px] border-[#EEEEEE]'>
        <Button
          className='flex items-center gap-1 px-3 py-[13px] text-[13px] font-normal text-[#616161] bg-transparent hover:bg-transparent cursor-pointer shadow-none'
          onClick={() => navigate(-1)}
        >
          <svg
            className='size-4'
            viewBox='0 0 16 17'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              d='M1.64941 8.06445C1.64941 7.89128 1.72005 7.73861 1.86133 7.60645L6.38672 3.08789C6.45964 3.01497 6.53255 2.96257 6.60547 2.93066C6.68294 2.89876 6.7627 2.88281 6.84473 2.88281C7.01335 2.88281 7.15462 2.93978 7.26855 3.05371C7.38704 3.16309 7.44629 3.30208 7.44629 3.4707C7.44629 3.55273 7.43034 3.63249 7.39844 3.70996C7.37109 3.78288 7.33008 3.84668 7.27539 3.90137L5.74414 5.45996L3.03711 7.93457L2.89355 7.59277L5.09473 7.45605H13.7422C13.9199 7.45605 14.0635 7.51302 14.1729 7.62695C14.2868 7.74089 14.3438 7.88672 14.3438 8.06445C14.3438 8.24219 14.2868 8.38802 14.1729 8.50195C14.0635 8.61589 13.9199 8.67285 13.7422 8.67285H5.09473L2.89355 8.53613L3.03711 8.20117L5.74414 10.6689L7.27539 12.2275C7.33008 12.2822 7.37109 12.3483 7.39844 12.4258C7.43034 12.4987 7.44629 12.5762 7.44629 12.6582C7.44629 12.8268 7.38704 12.9658 7.26855 13.0752C7.15462 13.1891 7.01335 13.2461 6.84473 13.2461C6.68066 13.2461 6.53255 13.1823 6.40039 13.0547L1.86133 8.52246C1.72005 8.3903 1.64941 8.23763 1.64941 8.06445Z'
              fill='currentColor'
            />
          </svg>
          <span>返回</span>
        </Button>
      </div>
      <div className='flex-1 flex flex-col items-center justify-center p-6'>
        <div className='flex flex-col items-center w-full max-w-sm'>
          {/* Logo 和标题 */}
          <div className='flex flex-col items-center mb-8'>
            <NavLink to='/' className='flex flex-col items-center gap-2 font-medium mb-4'>
              <div className='flex size-[45px] items-center justify-center rounded-md'>
                <img src={LogoUrl} alt='PlugMate' className='size-full' />
              </div>
              <span className='sr-only'>PlugMate.</span>
            </NavLink>
            <h1 className='text-[18px] font-[860] tracking-wider mb-2'>PlugMate</h1>
            <h2 className='font-normal text-xs text-[#757575] text-center'>
              不断扩展的设计资产和插件库
            </h2>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className='w-full mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-start gap-2'>
              <AlertCircle className='size-4 text-red-500 mt-0.5 flex-shrink-0' />
              <div className='flex-1'>
                <p className='text-sm text-red-700 font-medium'>登录失败</p>
                <p className='text-xs text-red-600 mt-1'>{error}</p>
              </div>
            </div>
          )}

          {/* 登录按钮 */}
          <div className='w-full space-y-3'>
            <Button
              className='w-full h-auto cursor-pointer !px-6 py-[12px] text-xs font-medium !bg-[#212121] !text-white gap-2 rounded-lg hover:!bg-[#333333] transition-colors'
              onClick={handleFigmaLogin}
              disabled={isLoading || isOAuthWindowOpen}
            >
              {isLoading || isOAuthWindowOpen ? (
                <>
                  <Loader2 className='size-4 animate-spin' />
                  <span>{isOAuthWindowOpen ? '请在弹窗中完成登录...' : '正在连接...'}</span>
                </>
              ) : (
                <>
                  <svg
                    className='size-4'
                    viewBox='0 0 17 16'
                    fill='none'
                    xmlns='http://www.w3.org/2000/svg'
                  >
                    <path
                      d='M6.4999 14.0008C7.60384 14.0008 8.49979 13.1047 8.49979 12.0005V10.0002H6.4999C5.39595 10.0002 4.5 10.8964 4.5 12.0005C4.5 13.1047 5.39595 14.0008 6.4999 14.0008Z'
                      fill='#0ACF83'
                    />
                    <path
                      d='M4.5 8.00006C4.5 6.89589 5.39595 5.99976 6.4999 5.99976H8.49979V10.0004H6.4999C5.39595 10.0004 4.5 9.10422 4.5 8.00006Z'
                      fill='#A259FF'
                    />
                    <path
                      d='M4.50073 3.99957C4.50073 2.8954 5.39669 1.99927 6.50063 1.99927H8.50053V5.99987H6.50063C5.39669 5.99987 4.50073 5.10373 4.50073 3.99957Z'
                      fill='#F24E1E'
                    />
                    <path
                      d='M8.50018 1.99927H10.5001C11.604 1.99927 12.5 2.8954 12.5 3.99957C12.5 5.10373 11.604 5.99987 10.5001 5.99987H8.50018V1.99927Z'
                      fill='#FF7262'
                    />
                    <path
                      d='M12.5 8.00006C12.5 9.10422 11.604 10.0004 10.5001 10.0004C9.39614 10.0004 8.50018 9.10422 8.50018 8.00006C8.50018 6.89589 9.39614 5.99976 10.5001 5.99976C11.604 5.99976 12.5 6.89589 12.5 8.00006Z'
                      fill='#1ABCFE'
                    />
                  </svg>
                  <span>Figma 账号一键登录</span>
                </>
              )}
            </Button>

            {/* 提示信息 */}
            <div className='text-center space-y-1'>
              <p className='text-xs text-[#757575]'>点击按钮将在新窗口中打开 Figma 授权页面</p>
              <div className='flex items-center justify-center gap-1 text-xs text-[#999999]'>
                <ExternalLink className='size-3' />
                <span>需要允许弹窗才能完成登录</span>
              </div>
            </div>
          </div>

          {/* 底部信息 */}
          <div className='mt-8 text-center'>
            <p className='text-xs text-[#999999]'>
              登录即表示您同意我们的
              <a
                href='https://plugmate.figmod.top/terms'
                target='_blank'
                rel='noopener noreferrer'
                className='text-[#666666] hover:text-[#333333] underline mx-1'
              >
                服务条款
              </a>
              和
              <a
                href='https://plugmate.figmod.top/privacy'
                target='_blank'
                rel='noopener noreferrer'
                className='text-[#666666] hover:text-[#333333] underline mx-1'
              >
                隐私政策
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
