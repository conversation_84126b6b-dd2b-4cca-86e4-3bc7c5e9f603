import { Link } from 'react-router';
import { useAppStore } from '../store/useAppStore';

export default function AboutPage() {
  const { settings } = useAppStore();

  return (
    <div className='min-h-screen p-8'>
      <div className='max-w-3xl mx-auto'>
        <Link to='/' className='inline-flex items-center text-blue-500 hover:text-blue-600 mb-6'>
          ← 返回首页
        </Link>

        <h1 className='text-3xl font-bold text-gray-900 dark:text-white mb-6'>
          关于 Plugmate Figma Plugin
        </h1>

        <div className='bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md mb-6'>
          <h2 className='text-xl font-semibold text-gray-900 dark:text-white mb-4'>项目简介</h2>
          <p className='text-gray-600 dark:text-gray-300 leading-relaxed mb-4'>
            这是一个基于 React 19 + TypeScript + Vite 构建的 Figma 插件项目。
            使用了现代化的前端技术栈，包括 React Router 进行路由管理和 Zustand 进行状态管理。
          </p>
          <p className='text-gray-600 dark:text-gray-300 leading-relaxed'>
            当前主题：
            <span className='font-medium'>
              {settings.theme === 'light' ? '浅色模式' : '深色模式'}
            </span>
            <br />
            当前语言：
            <span className='font-medium'>{settings.language === 'zh' ? '中文' : 'English'}</span>
          </p>
        </div>

        <div className='bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md mb-6'>
          <h2 className='text-xl font-semibold text-gray-900 dark:text-white mb-4'>技术栈</h2>
          <ul className='space-y-2 text-gray-600 dark:text-gray-300'>
            <li>
              • <span className='font-medium'>React 19</span> - 用户界面库
            </li>
            <li>
              • <span className='font-medium'>TypeScript</span> - 类型安全的 JavaScript
            </li>
            <li>
              • <span className='font-medium'>Vite</span> - 快速构建工具
            </li>
            <li>
              • <span className='font-medium'>Tailwind CSS</span> - 实用优先的 CSS 框架
            </li>
            <li>
              • <span className='font-medium'>React Router</span> - 客户端路由
            </li>
            <li>
              • <span className='font-medium'>Zustand</span> - 轻量级状态管理
            </li>
            <li>
              • <span className='font-medium'>pnpm</span> - 包管理器
            </li>
          </ul>
        </div>

        <div className='bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md'>
          <h2 className='text-xl font-semibold text-gray-900 dark:text-white mb-4'>功能特性</h2>
          <ul className='space-y-2 text-gray-600 dark:text-gray-300'>
            <li>• 多页面路由支持</li>
            <li>• 全局状态管理</li>
            <li>• 主题切换（浅色/深色）</li>
            <li>• 多语言支持</li>
            <li>• 响应式设计</li>
            <li>• TypeScript 完整类型支持</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
