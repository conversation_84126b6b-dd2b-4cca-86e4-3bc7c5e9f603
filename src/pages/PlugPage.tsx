import { NavLink, useNavigate, useParams } from 'react-router';
import { But<PERSON> } from '@/components/ui/button';

export default function PlugPage() {
  const { id } = useParams();
  console.info('PlugID: ', id);
  const navigate = useNavigate();

  return (
    <div className='relative h-screen flex flex-col'>
      <div className=''>
        <Button
          className='flex items-center gap-1 px-3 py-[13px] text-[13px] leading-[18px] font-normal text-[#616161] bg-transparent hover:bg-transparent cursor-pointer shadow-none'
          onClick={() => navigate(-1)}
        >
          <svg
            className='size-4'
            viewBox='0 0 16 17'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              d='M1.64941 8.06445C1.64941 7.89128 1.72005 7.73861 1.86133 7.60645L6.38672 3.08789C6.45964 3.01497 6.53255 2.96257 6.60547 2.93066C6.68294 2.89876 6.7627 2.88281 6.84473 2.88281C7.01335 2.88281 7.15462 2.93978 7.26855 3.05371C7.38704 3.16309 7.44629 3.30208 7.44629 3.4707C7.44629 3.55273 7.43034 3.63249 7.39844 3.70996C7.37109 3.78288 7.33008 3.84668 7.27539 3.90137L5.74414 5.45996L3.03711 7.93457L2.89355 7.59277L5.09473 7.45605H13.7422C13.9199 7.45605 14.0635 7.51302 14.1729 7.62695C14.2868 7.74089 14.3438 7.88672 14.3438 8.06445C14.3438 8.24219 14.2868 8.38802 14.1729 8.50195C14.0635 8.61589 13.9199 8.67285 13.7422 8.67285H5.09473L2.89355 8.53613L3.03711 8.20117L5.74414 10.6689L7.27539 12.2275C7.33008 12.2822 7.37109 12.3483 7.39844 12.4258C7.43034 12.4987 7.44629 12.5762 7.44629 12.6582C7.44629 12.8268 7.38704 12.9658 7.26855 13.0752C7.15462 13.1891 7.01335 13.2461 6.84473 13.2461C6.68066 13.2461 6.53255 13.1823 6.40039 13.0547L1.86133 8.52246C1.72005 8.3903 1.64941 8.23763 1.64941 8.06445Z'
              fill='currentColor'
            />
          </svg>
          <span>返回</span>
        </Button>
      </div>
      <div className='flex-1'>
        <div className='relative p-4 space-y-4'>
          <div className='flex items-center gap-[9px] overflow-hidden pb-4 border-b-[0.5px] border-[#eeeeee]'>
            <NavLink
              to={`/plug/1`}
              className='block size-11 rounded-md bg-gray-100 overflow-hidden border-[0.5px] border-[#eeeeee]'
            >
              <img
                src='https://a.plugmate.figmod.top/plugins-1753618163433-icon-7.png'
                alt='plug'
                className='size-full'
              />
            </NavLink>
            <div className='flex flex-col flex-1 overflow-hidden'>
              <NavLink
                to={`/plug/1`}
                className='text-sm leading-[17px] font-medium text-[#212121] truncate block'
              >
                Polygon Background Generator 丨多边形背景生成器
              </NavLink>
            </div>
          </div>
          <div className='flex items-center gap-2 text-xs text-center'>
            <div className='space-y-1 flex-1'>
              <p className='text-[#9E9E9E] flex items-center justify-center gap-[2px]'>
                <span>作者</span>
                <svg
                  className='size-2.5'
                  viewBox='0 0 11 11'
                  fill='none'
                  xmlns='http://www.w3.org/2000/svg'
                >
                  <g clip-path='url(#clip0_370_808)'>
                    <path
                      d='M7.16659 3.83399V5.91732C7.16659 6.24884 7.29829 6.56678 7.53271 6.8012C7.76713 7.03563 8.08507 7.16732 8.41659 7.16732C8.74811 7.16732 9.06605 7.03563 9.30047 6.8012C9.53489 6.56678 9.66659 6.24884 9.66659 5.91732V5.50065C9.66659 4.56203 9.34967 3.6509 8.76718 2.91489C8.18468 2.17888 7.37074 1.6611 6.45723 1.44545C5.54371 1.2298 4.58415 1.32891 3.73399 1.72672C2.88384 2.12453 2.19291 2.79773 1.77314 3.63726C1.35338 4.47679 1.22937 5.43346 1.42122 6.35227C1.61306 7.27108 2.10951 8.0982 2.83014 8.69962C3.55077 9.30104 4.45336 9.64153 5.39166 9.66591C6.32997 9.6903 7.24902 9.39716 7.99992 8.83399'
                      fill='white'
                      fill-opacity='0.12'
                    />
                    <path
                      d='M7.16659 3.83399V5.91732C7.16659 6.24884 7.29829 6.56678 7.53271 6.8012C7.76713 7.03563 8.08507 7.16732 8.41659 7.16732C8.74811 7.16732 9.06605 7.03563 9.30047 6.8012C9.53489 6.56678 9.66659 6.24884 9.66659 5.91732V5.50065C9.66659 4.56203 9.34967 3.6509 8.76718 2.91489C8.18468 2.17888 7.37074 1.6611 6.45723 1.44545C5.54371 1.2298 4.58415 1.32891 3.73399 1.72672C2.88384 2.12453 2.19291 2.79773 1.77314 3.63726C1.35338 4.47679 1.22937 5.43346 1.42122 6.35227C1.61306 7.27108 2.10951 8.0982 2.83014 8.69962C3.55077 9.30104 4.45336 9.64153 5.39166 9.66591C6.32997 9.6903 7.24902 9.39716 7.99992 8.83399'
                      stroke='#9E9E9E'
                      stroke-width='0.833333'
                      stroke-linecap='round'
                      stroke-linejoin='round'
                    />
                    <path
                      d='M5.49992 7.16732C6.42039 7.16732 7.16659 6.42113 7.16659 5.50065C7.16659 4.58018 6.42039 3.83398 5.49992 3.83398C4.57944 3.83398 3.83325 4.58018 3.83325 5.50065C3.83325 6.42113 4.57944 7.16732 5.49992 7.16732Z'
                      fill='white'
                      fill-opacity='0.12'
                      stroke='#9E9E9E'
                      stroke-width='0.833333'
                      stroke-linecap='round'
                      stroke-linejoin='round'
                    />
                  </g>
                  <defs>
                    <clipPath id='clip0_370_808'>
                      <rect width='10' height='10' fill='white' transform='translate(0.5 0.5)' />
                    </clipPath>
                  </defs>
                </svg>
              </p>
              <p className='text-[#757575]'>Eugenio Ciccale</p>
            </div>
            <div className='space-y-1 flex-1'>
              <p className='text-[#9E9E9E] flex items-center justify-center gap-[2px]'>
                <span>定价</span>
                <svg
                  className='size-2.5'
                  viewBox='0 0 10 11'
                  fill='none'
                  xmlns='http://www.w3.org/2000/svg'
                >
                  <g clip-path='url(#clip0_370_815)'>
                    <path
                      d='M5.24409 1.57815C5.08784 1.42186 4.87591 1.33403 4.65492 1.33398H1.66659C1.44557 1.33398 1.23361 1.42178 1.07733 1.57806C0.921049 1.73434 0.833252 1.9463 0.833252 2.16732V5.15565C0.833299 5.37665 0.921127 5.58857 1.07742 5.74482L4.70409 9.37148C4.89347 9.55967 5.1496 9.66529 5.41659 9.66529C5.68357 9.66529 5.9397 9.55967 6.12909 9.37148L8.87075 6.62982C9.05894 6.44044 9.16456 6.1843 9.16456 5.91732C9.16456 5.65034 9.05894 5.3942 8.87075 5.20482L5.24409 1.57815Z'
                      fill='white'
                      fill-opacity='0.12'
                      stroke='#9E9E9E'
                      stroke-width='0.833333'
                      stroke-linecap='round'
                      stroke-linejoin='round'
                    />
                  </g>
                  <defs>
                    <clipPath id='clip0_370_815'>
                      <rect width='10' height='10' fill='white' transform='translate(0 0.5)' />
                    </clipPath>
                  </defs>
                </svg>
              </p>
              <p className='text-[#757575]'>免费&付费</p>
            </div>
            <div className='space-y-1 flex-1'>
              <p className='text-[#9E9E9E] flex items-center justify-center gap-[2px]'>
                <span>类别</span>
                <svg
                  className='size-2.5'
                  viewBox='0 0 11 11'
                  fill='none'
                  xmlns='http://www.w3.org/2000/svg'
                >
                  <path
                    d='M3.95844 4.66634C3.90473 4.66927 3.85125 4.65729 3.80392 4.63173C3.75659 4.60616 3.71726 4.568 3.69026 4.52147C3.66327 4.47495 3.64967 4.42186 3.65096 4.36808C3.65226 4.3143 3.6684 4.26193 3.69761 4.21676L5.25011 1.74967C5.2745 1.70575 5.30983 1.66886 5.35268 1.6426C5.39552 1.61635 5.44442 1.6016 5.49463 1.5998C5.54485 1.59799 5.59468 1.6092 5.63929 1.63232C5.68391 1.65544 5.72179 1.68969 5.74927 1.73176L7.29177 4.20801C7.32218 4.25167 7.34006 4.30282 7.34348 4.35592C7.3469 4.40901 7.33574 4.46203 7.31119 4.50924C7.28664 4.55644 7.24965 4.59604 7.20422 4.62373C7.15879 4.65142 7.10665 4.66616 7.05344 4.66634H3.95844Z'
                    fill='white'
                    fill-opacity='0.12'
                    stroke='#9E9E9E'
                    stroke-width='0.833333'
                    stroke-linecap='round'
                    stroke-linejoin='round'
                  />
                  <path
                    d='M4.25 6.33398H2.16667C1.93655 6.33398 1.75 6.52053 1.75 6.75065V8.83398C1.75 9.0641 1.93655 9.25065 2.16667 9.25065H4.25C4.48012 9.25065 4.66667 9.0641 4.66667 8.83398V6.75065C4.66667 6.52053 4.48012 6.33398 4.25 6.33398Z'
                    fill='white'
                    fill-opacity='0.12'
                    stroke='#9E9E9E'
                    stroke-width='0.833333'
                    stroke-linecap='round'
                    stroke-linejoin='round'
                  />
                  <path
                    d='M7.79159 9.25065C8.597 9.25065 9.24992 8.59773 9.24992 7.79232C9.24992 6.9869 8.597 6.33398 7.79159 6.33398C6.98617 6.33398 6.33325 6.9869 6.33325 7.79232C6.33325 8.59773 6.98617 9.25065 7.79159 9.25065Z'
                    fill='white'
                    fill-opacity='0.12'
                    stroke='#9E9E9E'
                    stroke-width='0.833333'
                    stroke-linecap='round'
                    stroke-linejoin='round'
                  />
                </svg>
              </p>
              <p className='text-[#757575]'>设计</p>
            </div>
          </div>
          <NavLink
            to={`/plug/1`}
            className='rounded-md block bg-gray-100 overflow-hidden h-[196px] relative'
          >
            <img
              src='https://a.plugmate.figmod.top/plugins-1753618163978-6a461306a21252c11c917b23ebb37b2446b93d40.png'
              alt='plug-bg'
              className='size-full object-cover object-center'
            />
          </NavLink>
          <div className=''>
            <p className='text-xs leading-[20px] text-[#616161]'>
              制作自定义的多边形背景，并具有对几何、颜色和深度的广泛控制。提供将背景导出为 PNG
              或完全可编辑的 SVG
            </p>
          </div>
          <div className='flex items-center gap-5'>
            <div className='flex-1 min-w-0'>
              <Button
                size='lg'
                variant='secondary'
                className='text-xs cursor-pointer leading-5 flex items-center font-normal gap-0 w-full'
              >
                {true ? (
                  <>
                    <svg
                      className='size-5 mr-0.5'
                      viewBox='0 0 20 20'
                      fill='none'
                      xmlns='http://www.w3.org/2000/svg'
                    >
                      <path
                        d='M9.7227 4.22257C9.74826 4.17092 9.78775 4.12744 9.83672 4.09704C9.88568 4.06664 9.94216 4.05054 9.99979 4.05054C10.0574 4.05054 10.1139 4.06664 10.1629 4.09704C10.2118 4.12744 10.2513 4.17092 10.2769 4.22257L11.6244 6.9521C11.7132 7.13175 11.8443 7.28718 12.0063 7.40505C12.1684 7.52291 12.3566 7.59969 12.5549 7.62879L15.5685 8.06981C15.6256 8.07809 15.6793 8.10217 15.7234 8.13935C15.7675 8.17653 15.8004 8.22531 15.8182 8.28018C15.8361 8.33505 15.8382 8.39381 15.8244 8.44983C15.8106 8.50585 15.7814 8.55689 15.74 8.59717L13.5606 10.7194C13.4169 10.8595 13.3093 11.0324 13.2473 11.2232C13.1852 11.4141 13.1704 11.6171 13.2042 11.815L13.7187 14.8134C13.7288 14.8705 13.7226 14.9293 13.7009 14.983C13.6792 15.0367 13.6428 15.0833 13.5959 15.1174C13.549 15.1514 13.4935 15.1716 13.4357 15.1756C13.3779 15.1797 13.3201 15.1674 13.2689 15.1401L10.575 13.7237C10.3975 13.6305 10.2 13.5818 9.9995 13.5818C9.79901 13.5818 9.60152 13.6305 9.42402 13.7237L6.73065 15.1401C6.67951 15.1672 6.6218 15.1794 6.56408 15.1753C6.50636 15.1712 6.45094 15.1509 6.40415 15.1169C6.35735 15.0829 6.32104 15.0364 6.29935 14.9827C6.27766 14.9291 6.27147 14.8704 6.28147 14.8134L6.79541 11.8156C6.82936 11.6176 6.81465 11.4144 6.75255 11.2235C6.69046 11.0325 6.58284 10.8595 6.43897 10.7194L4.25955 8.59775C4.21789 8.55752 4.18837 8.5064 4.17436 8.45021C4.16034 8.39402 4.16238 8.33502 4.18026 8.27993C4.19813 8.22485 4.23112 8.17589 4.27546 8.13864C4.3198 8.10139 4.37371 8.07734 4.43106 8.06923L7.4441 7.62879C7.64259 7.59992 7.83109 7.52324 7.99338 7.40536C8.15567 7.28748 8.28688 7.13193 8.37572 6.9521L9.7227 4.22257Z'
                        fill='#F2994A'
                        stroke='#F2994A'
                        stroke-linecap='round'
                        stroke-linejoin='round'
                      />
                    </svg>
                    <span>已收藏</span>
                  </>
                ) : (
                  <>
                    <svg
                      className='size-5 mr-0.5'
                      viewBox='0 0 20 20'
                      fill='none'
                      xmlns='http://www.w3.org/2000/svg'
                    >
                      <path
                        d='M9.7227 4.22257C9.74826 4.17092 9.78775 4.12744 9.83672 4.09704C9.88568 4.06664 9.94216 4.05054 9.99979 4.05054C10.0574 4.05054 10.1139 4.06664 10.1629 4.09704C10.2118 4.12744 10.2513 4.17092 10.2769 4.22257L11.6244 6.9521C11.7132 7.13175 11.8443 7.28718 12.0063 7.40505C12.1684 7.52291 12.3566 7.59969 12.5549 7.62879L15.5685 8.06981C15.6256 8.07809 15.6793 8.10217 15.7234 8.13935C15.7675 8.17652 15.8004 8.22531 15.8182 8.28018C15.8361 8.33505 15.8382 8.39381 15.8244 8.44983C15.8106 8.50585 15.7814 8.55689 15.74 8.59717L13.5606 10.7194C13.4169 10.8595 13.3093 11.0324 13.2473 11.2232C13.1852 11.4141 13.1704 11.6171 13.2042 11.815L13.7187 14.8134C13.7288 14.8705 13.7226 14.9293 13.7009 14.983C13.6792 15.0367 13.6428 15.0833 13.5959 15.1174C13.549 15.1514 13.4935 15.1716 13.4357 15.1756C13.3779 15.1797 13.3201 15.1674 13.2689 15.1401L10.575 13.7237C10.3975 13.6305 10.2 13.5818 9.9995 13.5818C9.79901 13.5818 9.60152 13.6305 9.42402 13.7237L6.73065 15.1401C6.67951 15.1672 6.6218 15.1794 6.56408 15.1753C6.50636 15.1712 6.45094 15.1509 6.40415 15.1169C6.35735 15.0829 6.32104 15.0364 6.29935 14.9827C6.27766 14.9291 6.27147 14.8704 6.28147 14.8134L6.79541 11.8156C6.82936 11.6176 6.81465 11.4144 6.75255 11.2235C6.69046 11.0325 6.58284 10.8595 6.43897 10.7194L4.25955 8.59775C4.21789 8.55752 4.18837 8.5064 4.17436 8.45021C4.16034 8.39402 4.16238 8.33502 4.18026 8.27993C4.19813 8.22485 4.23112 8.17589 4.27546 8.13864C4.3198 8.10139 4.37371 8.07734 4.43106 8.06923L7.4441 7.62879C7.64259 7.59992 7.83109 7.52324 7.99338 7.40536C8.15567 7.28748 8.28688 7.13193 8.37572 6.9521L9.7227 4.22257Z'
                        stroke='currentColor'
                        stroke-linecap='round'
                        stroke-linejoin='round'
                      />
                    </svg>
                    <span>收藏</span>
                  </>
                )}
              </Button>
            </div>
            <NavLink to='/' className='w-full flex-1 min-w-0'>
              <Button
                size='lg'
                className='text-xs cursor-pointer leading-5 flex items-center font-normal gap-0 bg-[#5622DC] hover:bg-[#5622DC]/80 text-white w-full'
              >
                <svg
                  className='size-5 mr-0.5'
                  viewBox='0 0 20 20'
                  fill='none'
                  xmlns='http://www.w3.org/2000/svg'
                >
                  <path
                    d='M12.6367 3.91504C14.0579 3.91526 15.2099 5.06808 15.21 6.48926C15.2098 7.34156 14.7936 8.09422 14.1553 8.5625C14.7935 9.031 15.2099 9.78456 15.21 10.6367C15.2097 12.0578 14.0568 13.2097 12.6357 13.21C12.0424 13.2099 11.4978 13.0064 11.0625 12.6689V14.7842C11.0623 16.2054 9.90953 17.3574 8.48828 17.3574C7.06731 17.3571 5.91526 16.2052 5.91504 14.7842C5.91514 13.9321 6.33082 13.1785 6.96875 12.71C6.33103 12.2416 5.91517 11.4886 5.91504 10.6367C5.91511 9.78467 6.33082 9.03102 6.96875 8.5625C6.33102 8.09417 5.91517 7.34111 5.91504 6.48926C5.91512 5.06815 7.06723 3.91537 8.48828 3.91504H12.6367ZM8.48828 13.21C7.61956 13.2103 6.9152 13.9154 6.91504 14.7842C6.91526 15.6529 7.6196 16.3571 8.48828 16.3574C9.35725 16.3574 10.0623 15.6531 10.0625 14.7842V13.21H8.48828ZM8.48828 9.0625C7.61953 9.06283 6.91515 9.76792 6.91504 10.6367C6.91526 11.5054 7.6196 12.2096 8.48828 12.21H10.0625V9.0625H8.48828ZM12.6357 9.0625C11.7669 9.06272 11.0626 9.76785 11.0625 10.6367C11.0627 11.5055 11.767 12.2097 12.6357 12.21C13.5045 12.2097 14.2097 11.5055 14.21 10.6367C14.2098 9.76786 13.5046 9.06273 12.6357 9.0625ZM8.48828 4.91504C7.61951 4.91537 6.91512 5.62043 6.91504 6.48926C6.91526 7.35797 7.6196 8.06217 8.48828 8.0625H10.0625V4.91504H8.48828ZM11.0625 8.0625H12.6367C13.5055 8.06228 14.2097 7.35804 14.21 6.48926C14.2099 5.62036 13.5056 4.91526 12.6367 4.91504H11.0625V8.0625Z'
                    fill='currentColor'
                  />
                </svg>
                在 Figma 中打开
              </Button>
            </NavLink>
          </div>
        </div>
      </div>
    </div>
  );
}
