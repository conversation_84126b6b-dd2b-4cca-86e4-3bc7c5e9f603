import { useState } from 'react';
import { Link } from 'react-router';
import { useAuth } from '../hooks/useAuth';

export default function DashboardPage() {
  const { user, isLoading } = useAuth();
  const [counter, setCounter] = useState(0);

  const handleAsyncAction = async () => {
    // 模拟异步操作
    await new Promise((resolve) => setTimeout(resolve, 2000));
    setCounter((prev) => prev + 1);
  };

  // 如果用户未登录，显示登录提示
  if (!user?.id) {
    return (
      <div className='min-h-screen p-8'>
        <div className='max-w-2xl mx-auto text-center'>
          <Link to='/' className='inline-flex items-center text-blue-500 hover:text-blue-600 mb-6'>
            ← 返回首页
          </Link>

          <div className='bg-white dark:bg-gray-800 rounded-lg p-8 shadow-md'>
            <h1 className='text-2xl font-bold text-gray-900 dark:text-white mb-4'>访问受限</h1>
            <p className='text-gray-600 dark:text-gray-300 mb-6'>请先在首页登录后再访问控制台</p>
            <Link
              to='/'
              className='px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors'
            >
              前往登录
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50 dark:bg-gray-900 p-8'>
      <div className='max-w-4xl mx-auto'>
        <Link to='/' className='inline-flex items-center text-blue-500 hover:text-blue-600 mb-6'>
          ← 返回首页
        </Link>

        <h1 className='text-3xl font-bold text-gray-900 dark:text-white mb-8'>控制台</h1>

        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
          {/* 用户信息卡片 */}
          <div className='bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md'>
            <h2 className='text-xl font-semibold text-gray-900 dark:text-white mb-4'>用户信息</h2>
            <div className='space-y-3'>
              <div className='flex items-center space-x-3'>
                <div className='w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-lg'>
                  {user?.name?.charAt(0).toUpperCase() || 'U'}
                </div>
                <div>
                  <p className='font-medium text-gray-900 dark:text-white'>
                    {user?.name || 'Unknown'}
                  </p>
                  <p className='text-sm text-gray-600 dark:text-gray-300'>{user?.email || ''}</p>
                </div>
              </div>
              <div className='pt-3 border-t border-gray-200 dark:border-gray-700'>
                <p className='text-sm text-gray-600 dark:text-gray-300'>
                  用户 ID: <span className='font-mono'>{user?.id || 'N/A'}</span>
                </p>
              </div>
            </div>
          </div>

          {/* 计数器卡片 */}
          <div className='bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md'>
            <h2 className='text-xl font-semibold text-gray-900 dark:text-white mb-4'>交互演示</h2>
            <div className='text-center space-y-4'>
              <div className='text-4xl font-bold text-blue-500'>{counter}</div>
              <p className='text-gray-600 dark:text-gray-300'>异步操作计数器</p>
              <button
                type='button'
                onClick={handleAsyncAction}
                disabled={isLoading}
                className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                  isLoading
                    ? 'bg-gray-400 cursor-not-allowed text-white'
                    : 'bg-blue-500 hover:bg-blue-600 text-white'
                }`}
              >
                {isLoading ? '处理中...' : '执行异步操作'}
              </button>
            </div>
          </div>

          {/* 状态信息卡片 */}
          <div className='bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md lg:col-span-2'>
            <h2 className='text-xl font-semibold text-gray-900 dark:text-white mb-4'>应用状态</h2>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              <div className='bg-gray-50 dark:bg-gray-700 rounded-lg p-4'>
                <h3 className='font-medium text-gray-900 dark:text-white mb-2'>加载状态</h3>
                <p className='text-lg'>
                  <span
                    className={`inline-block w-3 h-3 rounded-full mr-2 ${
                      isLoading ? 'bg-yellow-500' : 'bg-green-500'
                    }`}
                  ></span>
                  {isLoading ? '加载中' : '空闲'}
                </p>
              </div>

              <div className='bg-gray-50 dark:bg-gray-700 rounded-lg p-4'>
                <h3 className='font-medium text-gray-900 dark:text-white mb-2'>操作次数</h3>
                <p className='text-lg font-bold text-blue-500'>{counter} 次</p>
              </div>

              <div className='bg-gray-50 dark:bg-gray-700 rounded-lg p-4'>
                <h3 className='font-medium text-gray-900 dark:text-white mb-2'>用户状态</h3>
                <p className='text-lg'>
                  <span className='inline-block w-3 h-3 rounded-full bg-green-500 mr-2'></span>
                  已登录
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
