import { Button } from './ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';

export default function HomeTabs() {
  return (
    <div className='flex gap-2 px-3 pt-4 items-center'>
      <Button variant='default' size='sm' className='cursor-pointer rounded-[4px]'>
        🏡 全部
      </Button>
      <Button variant='ghost' size='sm' className='cursor-pointer rounded-[4px] bg-white'>
        🪄 编辑&造型
      </Button>
      <Button variant='ghost' size='sm' className='cursor-pointer rounded-[4px] bg-white'>
        ⌨️ 开发
      </Button>
      <Select>
        <SelectTrigger className='w-auto max-w-36 !bg-white rounded-[4px] border-0 border-black/5 !text-[14px] !text-[#212121] shadow-none gap-0.5 ml-auto truncate overflow-hidden'>
          <SelectValue />
        </SelectTrigger>
        <SelectContent className='!bg-white !text-[14px] !text-[#212121] border-none rounded-lg p-1'>
          <SelectItem className='rounded' value='edit'>
            🪄 编辑&造型
          </SelectItem>
          <SelectItem className='rounded' value='dev'>
            ⌨️ 开发
          </SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}
