import { Input } from './ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';

export default function Search() {
  return (
    <div className='flex gap-3 px-3 pt-4'>
      <div className='flex-1 flex items-center gap-2 border-[0.5px] border-black/5 rounded-[6px] px-3 py-2 bg-white'>
        <svg
          className='size-4 text-[#9E9E9E]'
          viewBox='0 0 16 16'
          fill='none'
          xmlns='http://www.w3.org/2000/svg'
        >
          <path
            d='M1.45996 7.26172C1.45996 6.55078 1.59326 5.88428 1.85986 5.26221C2.1307 4.6359 2.50309 4.08577 2.97705 3.61182C3.45101 3.13363 3.99902 2.76123 4.62109 2.49463C5.2474 2.22803 5.91602 2.09473 6.62695 2.09473C7.34212 2.09473 8.01074 2.22803 8.63281 2.49463C9.25911 2.76123 9.80924 3.13363 10.2832 3.61182C10.7572 4.08577 11.1274 4.6359 11.394 5.26221C11.6649 5.88428 11.8003 6.55078 11.8003 7.26172C11.8003 7.82031 11.7135 8.3514 11.54 8.85498C11.3708 9.35856 11.1359 9.81771 10.8354 10.2324L13.73 13.1396C13.8188 13.2243 13.8844 13.3237 13.9268 13.438C13.9733 13.5522 13.9966 13.6729 13.9966 13.7998C13.9966 13.9775 13.9564 14.1383 13.876 14.2822C13.7956 14.4261 13.6855 14.5382 13.5459 14.6187C13.4062 14.7033 13.2454 14.7456 13.0635 14.7456C12.9365 14.7456 12.8138 14.7223 12.6953 14.6758C12.5811 14.6335 12.4774 14.5658 12.3843 14.4727L9.4707 11.5591C9.06445 11.8299 8.62012 12.0436 8.1377 12.2002C7.65951 12.3525 7.15592 12.4287 6.62695 12.4287C5.91602 12.4287 5.2474 12.2954 4.62109 12.0288C3.99902 11.7622 3.45101 11.3919 2.97705 10.918C2.50309 10.444 2.1307 9.896 1.85986 9.27393C1.59326 8.64762 1.45996 7.97689 1.45996 7.26172ZM2.78662 7.26172C2.78662 7.79492 2.88395 8.29427 3.07861 8.75977C3.27751 9.22103 3.55257 9.62728 3.90381 9.97852C4.25928 10.3298 4.66976 10.6069 5.13525 10.8101C5.60075 11.009 6.09798 11.1084 6.62695 11.1084C7.16016 11.1084 7.65739 11.009 8.11865 10.8101C8.58415 10.6069 8.99251 10.3298 9.34375 9.97852C9.69499 9.62728 9.97005 9.22103 10.1689 8.75977C10.3721 8.29427 10.4736 7.79492 10.4736 7.26172C10.4736 6.73275 10.3721 6.23551 10.1689 5.77002C9.97005 5.30452 9.69499 4.89616 9.34375 4.54492C8.99251 4.18945 8.58415 3.91439 8.11865 3.71973C7.65739 3.52083 7.16016 3.42139 6.62695 3.42139C6.09798 3.42139 5.60075 3.52083 5.13525 3.71973C4.66976 3.91439 4.25928 4.18945 3.90381 4.54492C3.55257 4.89616 3.27751 5.30452 3.07861 5.77002C2.88395 6.23551 2.78662 6.73275 2.78662 7.26172Z'
            fill='currentColor'
          />
        </svg>
        <Input
          type='text'
          placeholder='搜索'
          className='w-full flex-1 text-[14px] leading-5 text-[#212121] placeholder:text-[#9E9E9E] p-0 border-none !bg-inherit shadow-none h-5 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-none focus-visible:outline-none focus-visible:ring-shadow-none'
        />
      </div>
      <Select defaultValue='all'>
        <SelectTrigger className='w-auto max-w-36 !bg-white rounded-[6px] border-[0.5px] border-black/5 !text-[14px] !text-[#212121] shadow-none gap-0.5'>
          <SelectValue placeholder='选择分类' />
        </SelectTrigger>
        <SelectContent className='!bg-white !text-[14px] !text-[#212121] border-none rounded-lg p-1'>
          <SelectItem className='rounded' value='all'>
            🏡 全部
          </SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}
