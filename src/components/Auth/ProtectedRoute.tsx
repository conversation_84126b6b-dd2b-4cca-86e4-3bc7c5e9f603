import { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router';
import { useAuth } from '@/hooks/useAuth';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requireAdmin?: boolean;
  requireMember?: boolean;
  fallbackPath?: string;
  loadingComponent?: React.ReactNode;
}

export default function ProtectedRoute({
  children,
  requireAuth = true,
  requireAdmin = false,
  requireMember = false,
  fallbackPath = '/login',
  loadingComponent,
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, user, validateSession, error } = useAuth();
  const location = useLocation();

  // 显示加载状态
  if (isLoading) {
    if (loadingComponent) {
      return <>{loadingComponent}</>;
    }

    return (
      <div className='flex items-center justify-center min-h-screen'>
        <div className='flex flex-col items-center gap-3'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-[#212121]'></div>
          <p className='text-sm text-[#666666]'>验证登录状态...</p>
        </div>
      </div>
    );
  }

  // 检查是否需要认证
  if (requireAuth && !isAuthenticated) {
    // 保存当前路径，登录后可以重定向回来
    return (
      <Navigate to={fallbackPath} state={{ from: location.pathname + location.search }} replace />
    );
  }

  // 检查管理员权限
  if (requireAdmin && isAuthenticated && user?.role !== 'admin') {
    return (
      <div className='flex flex-col items-center justify-center min-h-screen p-6'>
        <div className='text-center max-w-sm'>
          <h1 className='text-lg font-semibold text-[#212121] mb-2'>访问受限</h1>
          <p className='text-sm text-[#666666] mb-4'>此页面需要管理员权限才能访问。</p>
          <button
            onClick={() => window.history.back()}
            className='px-4 py-2 text-sm font-medium text-white bg-[#212121] rounded-lg hover:bg-[#333333] transition-colors'
          >
            返回上一页
          </button>
        </div>
      </div>
    );
  }

  // 检查会员权限
  if (requireMember && isAuthenticated) {
    const hasMemberAccess = user?.role === 'admin' || user?.isMember === 1;
    if (!hasMemberAccess) {
      return (
        <div className='flex flex-col items-center justify-center min-h-screen p-6'>
          <div className='text-center max-w-sm'>
            <h1 className='text-lg font-semibold text-[#212121] mb-2'>会员专属</h1>
            <p className='text-sm text-[#666666] mb-4'>此内容需要会员权限才能访问。</p>
            <button
              onClick={() => window.history.back()}
              className='px-4 py-2 text-sm font-medium text-white bg-[#212121] rounded-lg hover:bg-[#333333] transition-colors'
            >
              返回上一页
            </button>
          </div>
        </div>
      );
    }
  }

  // 如果用户已认证但有错误，尝试验证会话
  if (isAuthenticated && error) {
    useEffect(() => {
      validateSession();
    }, [validateSession]);

    return (
      <div className='flex flex-col items-center justify-center min-h-screen p-6'>
        <div className='text-center max-w-sm'>
          <h1 className='text-lg font-semibold text-[#212121] mb-2'>会话异常</h1>
          <p className='text-sm text-[#666666] mb-4'>正在重新验证登录状态...</p>
          <div className='animate-spin rounded-full h-6 w-6 border-b-2 border-[#212121] mx-auto'></div>
        </div>
      </div>
    );
  }

  // 所有检查通过，渲染子组件
  return <>{children}</>;
}

// 高阶组件版本
export function withProtectedRoute<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<ProtectedRouteProps, 'children'> = {},
) {
  return function ProtectedComponent(props: P) {
    return (
      <ProtectedRoute {...options}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}

// 预设的保护路由组件
export function AdminRoute({ children }: { children: React.ReactNode }) {
  return <ProtectedRoute requireAdmin={true}>{children}</ProtectedRoute>;
}

export function MemberRoute({ children }: { children: React.ReactNode }) {
  return <ProtectedRoute requireMember={true}>{children}</ProtectedRoute>;
}

export function AuthRoute({ children }: { children: React.ReactNode }) {
  return <ProtectedRoute requireAuth={true}>{children}</ProtectedRoute>;
}
