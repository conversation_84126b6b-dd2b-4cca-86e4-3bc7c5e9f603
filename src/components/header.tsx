import { NavLink } from 'react-router';
import { Button } from './ui/button';

export default function HeaderBox() {
  return (
    <header className='flex items-center px-3 py-2 bg-white'>
      <div className='flex items-center gap-3'>
        <NavLink to='/' className='size-6 !p-0 cursor-pointer'>
          <svg
            className='size-full'
            viewBox='0 0 24 24'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              d='M14.0381 5C16.7783 5 19 7.22173 19 9.96191C18.9998 12.7019 16.7781 14.9229 14.0381 14.9229H9V19H6V16L10 11.9229H14.0381C15.1213 11.9229 15.9998 11.0451 16 9.96191C16 8.87859 15.1214 8 14.0381 8H9V5H14.0381Z'
              fill='currentColor'
            />
            <path d='M9 11H6V8H9V11Z' fill='currentColor' />
          </svg>
          <span className='sr-only'>首页</span>
        </NavLink>
        <NavLink to='/search' className='size-6 !p-0 cursor-pointer'>
          <svg
            className='size-full'
            viewBox='0 0 24 24'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              d='M19 19L14.5 14.5'
              stroke='currentColor'
              stroke-width='1.4'
              stroke-linecap='round'
              stroke-linejoin='round'
            />
            <path
              d='M10.4444 15.8889C13.4513 15.8889 15.8889 13.4513 15.8889 10.4444C15.8889 7.43756 13.4513 5 10.4444 5C7.43756 5 5 7.43756 5 10.4444C5 13.4513 7.43756 15.8889 10.4444 15.8889Z'
              stroke='currentColor'
              stroke-width='1.4'
              stroke-linecap='round'
              stroke-linejoin='round'
            />
          </svg>
          <span className='sr-only'>搜索</span>
        </NavLink>
        <Button variant='ghost' size='icon' className='size-6 !p-0 cursor-pointer'>
          <svg
            className='size-full'
            viewBox='0 0 24 24'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              d='M11.6672 5.06703C11.6979 5.00505 11.7453 4.95288 11.804 4.9164C11.8628 4.87992 11.9306 4.8606 11.9997 4.8606C12.0689 4.8606 12.1367 4.87992 12.1954 4.9164C12.2542 4.95288 12.3016 5.00505 12.3322 5.06703L13.9493 8.34247C14.0558 8.55806 14.2131 8.74457 14.4076 8.88601C14.602 9.02745 14.8279 9.11958 15.0659 9.1545L18.6822 9.68373C18.7507 9.69366 18.8151 9.72256 18.8681 9.76717C18.921 9.81178 18.9604 9.87032 18.9818 9.93616C19.0033 10.002 19.0058 10.0725 18.9892 10.1398C18.9727 10.207 18.9376 10.2682 18.888 10.3166L16.2727 12.8633C16.1002 13.0313 15.9712 13.2388 15.8967 13.4678C15.8222 13.6968 15.8044 13.9405 15.845 14.1779L16.4624 17.7761C16.4745 17.8446 16.4671 17.9151 16.4411 17.9796C16.415 18.044 16.3714 18.0999 16.3151 18.1408C16.2588 18.1817 16.1922 18.2059 16.1228 18.2107C16.0534 18.2155 15.9841 18.2008 15.9227 18.1681L12.69 16.4684C12.477 16.3566 12.24 16.2981 11.9994 16.2981C11.7588 16.2981 11.5218 16.3566 11.3088 16.4684L8.07676 18.1681C8.01539 18.2006 7.94613 18.2152 7.87687 18.2103C7.8076 18.2053 7.74111 18.1811 7.68495 18.1402C7.62879 18.0994 7.58522 18.0436 7.5592 17.9792C7.53317 17.9149 7.52574 17.8445 7.53774 17.7761L8.15446 14.1786C8.1952 13.9411 8.17755 13.6973 8.10304 13.4681C8.02852 13.239 7.89938 13.0314 7.72675 12.8633L5.11143 10.3173C5.06145 10.269 5.02603 10.2076 5.0092 10.1402C4.99238 10.0728 4.99483 10.002 5.01628 9.93587C5.03773 9.86977 5.07732 9.81102 5.13053 9.76632C5.18374 9.72162 5.24843 9.69276 5.31724 9.68303L8.9329 9.1545C9.17109 9.11985 9.39729 9.02784 9.59203 8.88638C9.78678 8.74493 9.94423 8.55827 10.0508 8.34247L11.6672 5.06703Z'
              stroke='currentColor'
              stroke-width='1.4'
              stroke-linecap='round'
              stroke-linejoin='round'
            />
          </svg>
          <span className='sr-only'>收藏</span>
        </Button>
      </div>
      <div className='flex items-center gap-4 ml-auto'>
        <Button variant='ghost' size='icon' className='size-6 !p-0 cursor-pointer'>
          <svg
            className='size-full'
            viewBox='0 0 24 24'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              d='M17.0037 14.9869L18.9877 7.81387C19.0072 7.74237 19.0034 7.66655 18.977 7.59733C18.9506 7.5281 18.9029 7.46904 18.8408 7.42864C18.7787 7.38825 18.7054 7.3686 18.6314 7.37254C18.5574 7.37647 18.4865 7.40377 18.4291 7.45052L15.4348 10.0156C15.3551 10.0815 15.2619 10.1289 15.1617 10.1544C15.0616 10.1799 14.957 10.1829 14.8556 10.1633C14.7541 10.1436 14.6582 10.1017 14.5749 10.0406C14.4915 9.97946 14.4228 9.90065 14.3735 9.80981L12.3068 5.88654C12.2766 5.83165 12.2322 5.78589 12.1783 5.75401C12.1243 5.72214 12.0628 5.70532 12.0002 5.70532C11.9375 5.70532 11.876 5.72214 11.8221 5.75401C11.7682 5.78589 11.7238 5.83165 11.6936 5.88654L9.6269 9.81051C9.57762 9.90135 9.50883 9.98016 9.42548 10.0413C9.34213 10.1024 9.24629 10.1443 9.14483 10.164C9.04336 10.1836 8.9388 10.1806 8.83865 10.1551C8.7385 10.1296 8.64524 10.0822 8.56558 10.0163L5.57201 7.45122C5.51453 7.40447 5.4437 7.37717 5.36972 7.37324C5.29573 7.3693 5.2224 7.38895 5.16029 7.42934C5.09818 7.46974 5.05049 7.5288 5.02409 7.59803C4.99768 7.66725 4.99392 7.74307 5.01335 7.81457L6.99669 14.9869M17.0037 14.9869H6.99669M17.0037 14.9869L18 18.5H6L6.99669 14.9869'
              stroke='currentColor'
              stroke-width='1.4'
              stroke-linecap='round'
              stroke-linejoin='round'
            />
          </svg>
          <span className='sr-only'>会员</span>
        </Button>
        <Button variant='ghost' size='icon' className='size-6 !p-0 cursor-pointer'>
          <svg
            className='size-full'
            viewBox='0 0 24 24'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              d='M10.8456 17.9995C10.9626 18.2022 11.1309 18.3705 11.3336 18.4875C11.5362 18.6045 11.7661 18.6661 12.0002 18.6661C12.2342 18.6661 12.4641 18.6045 12.6668 18.4875C12.8694 18.3705 13.0377 18.2022 13.1548 17.9995'
              stroke='currentColor'
              stroke-width='1.4'
              stroke-linecap='round'
              stroke-linejoin='round'
            />
            <path
              d='M6.17527 14.2171C6.08818 14.3126 6.03071 14.4313 6.00985 14.5588C5.98898 14.6863 6.00562 14.8171 6.05775 14.9353C6.10987 15.0536 6.19522 15.1541 6.30343 15.2247C6.41163 15.2953 6.53803 15.3329 6.66723 15.333H17.3331C17.4623 15.3331 17.5888 15.2956 17.697 15.2251C17.8053 15.1546 17.8908 15.0542 17.943 14.9361C17.9953 14.8179 18.0121 14.6871 17.9914 14.5596C17.9707 14.4321 17.9134 14.3133 17.8264 14.2178C16.9398 13.3038 15.9999 12.3326 15.9999 9.33346C15.9999 8.27267 15.5785 7.25532 14.8284 6.50523C14.0783 5.75514 13.061 5.33374 12.0002 5.33374C10.9394 5.33374 9.92205 5.75514 9.17196 6.50523C8.42187 7.25532 8.00047 8.27267 8.00047 9.33346C8.00047 12.3326 7.05987 13.3038 6.17527 14.2171Z'
              stroke='currentColor'
              stroke-width='1.4'
              stroke-linecap='round'
              stroke-linejoin='round'
            />
          </svg>
          <span className='sr-only'>消息</span>
        </Button>
        <NavLink to='/login' className='cursor-pointer'>
          <Button className='cursor-pointer rounded px-4 py-[5.5px] text-xs leading-3 h-7 font-medium'>
            登录
          </Button>
        </NavLink>
      </div>
    </header>
  );
}
