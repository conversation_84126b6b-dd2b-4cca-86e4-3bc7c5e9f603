import { NavLink } from 'react-router';
import { Button } from './ui/button';

export default function Plug() {
  return (
    <div className='relative p-3 rounded-[12px] bg-white'>
      <div className='flex items-center gap-[9px] mb-3 overflow-hidden'>
        <NavLink
          to={`/plug/1`}
          preventScrollReset
          className='block size-11 rounded-md bg-gray-100 overflow-hidden border-[0.5px] border-[#eeeeee]'
        >
          <img
            src='https://a.plugmate.figmod.top/plugins-1753618163433-icon-7.png'
            alt='plug'
            className='size-full'
          />
        </NavLink>
        <div className='flex flex-col flex-1 overflow-hidden'>
          <NavLink
            to={`/plug/1`}
            preventScrollReset
            className='text-sm leading-[17px] font-medium text-[#212121] truncate block'
          >
            Polygon Background Generator 丨多边形背景生成器
          </NavLink>
          <p className='text-[11px] text-[#9e9e9e]'>by <PERSON><PERSON><PERSON></p>
        </div>
      </div>
      <NavLink
        to={`/plug/1`}
        preventScrollReset
        className='rounded-md block bg-gray-100 overflow-hidden mb-4 h-[196px] relative'
      >
        <img
          src='https://a.plugmate.figmod.top/plugins-1753618163978-6a461306a21252c11c917b23ebb37b2446b93d40.png'
          alt='plug-bg'
          className='size-full object-cover object-center'
        />
      </NavLink>
      <div className='mb-3'>
        <p className='text-xs leading-[20px] text-[#616161]'>
          制作自定义的多边形背景，并具有对几何、颜色和深度的广泛控制。提供将背景导出为 PNG
          或完全可编辑的 SVG
        </p>
      </div>
      <div className='flex items-center gap-2 justify-end'>
        <Button
          variant='ghost'
          size='sm'
          className='text-xs cursor-pointer leading-5 text-[#424242] flex items-center font-normal gap-0 p-0 !bg-transparent h-auto'
        >
          {true ? (
            <>
              <svg
                className='size-5 mr-0.5'
                viewBox='0 0 20 20'
                fill='none'
                xmlns='http://www.w3.org/2000/svg'
              >
                <path
                  d='M9.7227 4.22257C9.74826 4.17092 9.78775 4.12744 9.83672 4.09704C9.88568 4.06664 9.94216 4.05054 9.99979 4.05054C10.0574 4.05054 10.1139 4.06664 10.1629 4.09704C10.2118 4.12744 10.2513 4.17092 10.2769 4.22257L11.6244 6.9521C11.7132 7.13175 11.8443 7.28718 12.0063 7.40505C12.1684 7.52291 12.3566 7.59969 12.5549 7.62879L15.5685 8.06981C15.6256 8.07809 15.6793 8.10217 15.7234 8.13935C15.7675 8.17653 15.8004 8.22531 15.8182 8.28018C15.8361 8.33505 15.8382 8.39381 15.8244 8.44983C15.8106 8.50585 15.7814 8.55689 15.74 8.59717L13.5606 10.7194C13.4169 10.8595 13.3093 11.0324 13.2473 11.2232C13.1852 11.4141 13.1704 11.6171 13.2042 11.815L13.7187 14.8134C13.7288 14.8705 13.7226 14.9293 13.7009 14.983C13.6792 15.0367 13.6428 15.0833 13.5959 15.1174C13.549 15.1514 13.4935 15.1716 13.4357 15.1756C13.3779 15.1797 13.3201 15.1674 13.2689 15.1401L10.575 13.7237C10.3975 13.6305 10.2 13.5818 9.9995 13.5818C9.79901 13.5818 9.60152 13.6305 9.42402 13.7237L6.73065 15.1401C6.67951 15.1672 6.6218 15.1794 6.56408 15.1753C6.50636 15.1712 6.45094 15.1509 6.40415 15.1169C6.35735 15.0829 6.32104 15.0364 6.29935 14.9827C6.27766 14.9291 6.27147 14.8704 6.28147 14.8134L6.79541 11.8156C6.82936 11.6176 6.81465 11.4144 6.75255 11.2235C6.69046 11.0325 6.58284 10.8595 6.43897 10.7194L4.25955 8.59775C4.21789 8.55752 4.18837 8.5064 4.17436 8.45021C4.16034 8.39402 4.16238 8.33502 4.18026 8.27993C4.19813 8.22485 4.23112 8.17589 4.27546 8.13864C4.3198 8.10139 4.37371 8.07734 4.43106 8.06923L7.4441 7.62879C7.64259 7.59992 7.83109 7.52324 7.99338 7.40536C8.15567 7.28748 8.28688 7.13193 8.37572 6.9521L9.7227 4.22257Z'
                  fill='#F2994A'
                  stroke='#F2994A'
                  stroke-linecap='round'
                  stroke-linejoin='round'
                />
              </svg>
              <span>已收藏</span>
            </>
          ) : (
            <>
              <svg
                className='size-5 mr-0.5'
                viewBox='0 0 20 20'
                fill='none'
                xmlns='http://www.w3.org/2000/svg'
              >
                <path
                  d='M9.7227 4.22257C9.74826 4.17092 9.78775 4.12744 9.83672 4.09704C9.88568 4.06664 9.94216 4.05054 9.99979 4.05054C10.0574 4.05054 10.1139 4.06664 10.1629 4.09704C10.2118 4.12744 10.2513 4.17092 10.2769 4.22257L11.6244 6.9521C11.7132 7.13175 11.8443 7.28718 12.0063 7.40505C12.1684 7.52291 12.3566 7.59969 12.5549 7.62879L15.5685 8.06981C15.6256 8.07809 15.6793 8.10217 15.7234 8.13935C15.7675 8.17652 15.8004 8.22531 15.8182 8.28018C15.8361 8.33505 15.8382 8.39381 15.8244 8.44983C15.8106 8.50585 15.7814 8.55689 15.74 8.59717L13.5606 10.7194C13.4169 10.8595 13.3093 11.0324 13.2473 11.2232C13.1852 11.4141 13.1704 11.6171 13.2042 11.815L13.7187 14.8134C13.7288 14.8705 13.7226 14.9293 13.7009 14.983C13.6792 15.0367 13.6428 15.0833 13.5959 15.1174C13.549 15.1514 13.4935 15.1716 13.4357 15.1756C13.3779 15.1797 13.3201 15.1674 13.2689 15.1401L10.575 13.7237C10.3975 13.6305 10.2 13.5818 9.9995 13.5818C9.79901 13.5818 9.60152 13.6305 9.42402 13.7237L6.73065 15.1401C6.67951 15.1672 6.6218 15.1794 6.56408 15.1753C6.50636 15.1712 6.45094 15.1509 6.40415 15.1169C6.35735 15.0829 6.32104 15.0364 6.29935 14.9827C6.27766 14.9291 6.27147 14.8704 6.28147 14.8134L6.79541 11.8156C6.82936 11.6176 6.81465 11.4144 6.75255 11.2235C6.69046 11.0325 6.58284 10.8595 6.43897 10.7194L4.25955 8.59775C4.21789 8.55752 4.18837 8.5064 4.17436 8.45021C4.16034 8.39402 4.16238 8.33502 4.18026 8.27993C4.19813 8.22485 4.23112 8.17589 4.27546 8.13864C4.3198 8.10139 4.37371 8.07734 4.43106 8.06923L7.4441 7.62879C7.64259 7.59992 7.83109 7.52324 7.99338 7.40536C8.15567 7.28748 8.28688 7.13193 8.37572 6.9521L9.7227 4.22257Z'
                  stroke='currentColor'
                  stroke-linecap='round'
                  stroke-linejoin='round'
                />
              </svg>
              <span>收藏</span>
            </>
          )}
        </Button>
        <NavLink to='/' className='text-xs leading-5 text-[#5622DC] flex items-center'>
          <svg
            className='size-5 mr-0.5'
            viewBox='0 0 20 20'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              d='M12.6367 3.91504C14.0579 3.91526 15.2099 5.06808 15.21 6.48926C15.2098 7.34156 14.7936 8.09422 14.1553 8.5625C14.7935 9.031 15.2099 9.78456 15.21 10.6367C15.2097 12.0578 14.0568 13.2097 12.6357 13.21C12.0424 13.2099 11.4978 13.0064 11.0625 12.6689V14.7842C11.0623 16.2054 9.90953 17.3574 8.48828 17.3574C7.06731 17.3571 5.91526 16.2052 5.91504 14.7842C5.91514 13.9321 6.33082 13.1785 6.96875 12.71C6.33103 12.2416 5.91517 11.4886 5.91504 10.6367C5.91511 9.78467 6.33082 9.03102 6.96875 8.5625C6.33102 8.09417 5.91517 7.34111 5.91504 6.48926C5.91512 5.06815 7.06723 3.91537 8.48828 3.91504H12.6367ZM8.48828 13.21C7.61956 13.2103 6.9152 13.9154 6.91504 14.7842C6.91526 15.6529 7.6196 16.3571 8.48828 16.3574C9.35725 16.3574 10.0623 15.6531 10.0625 14.7842V13.21H8.48828ZM8.48828 9.0625C7.61953 9.06283 6.91515 9.76792 6.91504 10.6367C6.91526 11.5054 7.6196 12.2096 8.48828 12.21H10.0625V9.0625H8.48828ZM12.6357 9.0625C11.7669 9.06272 11.0626 9.76785 11.0625 10.6367C11.0627 11.5055 11.767 12.2097 12.6357 12.21C13.5045 12.2097 14.2097 11.5055 14.21 10.6367C14.2098 9.76786 13.5046 9.06273 12.6357 9.0625ZM8.48828 4.91504C7.61951 4.91537 6.91512 5.62043 6.91504 6.48926C6.91526 7.35797 7.6196 8.06217 8.48828 8.0625H10.0625V4.91504H8.48828ZM11.0625 8.0625H12.6367C13.5055 8.06228 14.2097 7.35804 14.21 6.48926C14.2099 5.62036 13.5056 4.91526 12.6367 4.91504H11.0625V8.0625Z'
              fill='currentColor'
            />
          </svg>
          在 Figma 中打开
        </NavLink>
      </div>
    </div>
  );
}
