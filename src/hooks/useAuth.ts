import { useCallback, useEffect, useRef, useState } from 'react';
import { useAuth as useAuthStore } from '../store/useAppStore';
import type { User } from '../utils/api';
import {
  AuthErrorType,
  handleAuthError,
  logoutUser,
  setupOAuthMessageListener,
  validateCurrentSession,
} from '../utils/auth';

// 认证状态 Hook
export const useAuth = () => {
  // 使用 Zustand 的认证状态
  const authStore = useAuthStore();

  // 本地状态
  const [isValidating, setIsValidating] = useState(false);
  const [oauthWindow, setOAuthWindow] = useState<Window | null>(null);
  const authCheckTimer = useRef<NodeJS.Timeout | null>(null);

  // 处理 OAuth 登录
  const handleLogin = useCallback(async () => {
    try {
      authStore.setAuthLoading(true);
      authStore.setAuthError(null);

      // 通知主线程开始 OAuth 登录
      parent.postMessage(
        {
          pluginMessage: {
            type: 'oauth-login',
          },
        },
        '*',
      );

      // 在新窗口中打开 OAuth 登录页面
      const loginWindow = window.open(
        'https://plugmate.figmod.top/login',
        '_blank',
        'width=600,height=700',
      );
      setOAuthWindow(loginWindow);

      // 监听 OAuth 成功消息
      const cleanupListener = setupOAuthMessageListener(async (_user: User) => {
        try {
          // 验证会话并获取完整用户信息
          const fullUser = await validateCurrentSession();
          if (fullUser) {
            authStore.setUser(fullUser);
            authStore.setLastAuthCheck(Date.now());
          } else {
            throw new Error('Session validation failed');
          }
        } catch (error) {
          console.error('Session validation after OAuth:', error);
          authStore.setAuthError('登录验证失败，请重试');
        } finally {
          authStore.setAuthLoading(false);
          if (loginWindow) {
            loginWindow.close();
          }
          setOAuthWindow(null);
        }
      });

      // 监听窗口关闭事件
      const checkWindowClosed = setInterval(() => {
        if (loginWindow?.closed) {
          clearInterval(checkWindowClosed);
          cleanupListener();
          setOAuthWindow(null);
          if (!authStore.isAuthenticated) {
            authStore.setAuthLoading(false);
          }
        }
      }, 1000);

      // 30 秒后自动清理
      setTimeout(() => {
        clearInterval(checkWindowClosed);
        cleanupListener();
        if (loginWindow && !loginWindow.closed) {
          loginWindow.close();
        }
        setOAuthWindow(null);
        if (!authStore.isAuthenticated) {
          authStore.setAuthLoading(false);
          authStore.setAuthError('登录超时，请重试');
        }
      }, 30000);
    } catch (error) {
      const { message } = handleAuthError(error);
      authStore.setAuthError(message);
      authStore.setAuthLoading(false);
      console.error('Login error:', error);
    }
  }, [authStore]);

  // 处理登出
  const handleLogout = useCallback(async () => {
    try {
      authStore.setAuthLoading(true);

      // 调用登出 API
      await logoutUser();

      // 清除本地状态
      authStore.clearAuth();

      // 清除 OAuth 窗口
      if (oauthWindow && !oauthWindow.closed) {
        oauthWindow.close();
      }
      setOAuthWindow(null);
    } catch (error) {
      console.error('Logout error:', error);
      // 即使 API 失败，也要清除本地状态
      authStore.clearAuth();
    } finally {
      authStore.setAuthLoading(false);
    }
  }, [authStore, oauthWindow]);

  // 验证当前会话
  const validateSession = useCallback(async () => {
    if (isValidating) return;

    try {
      setIsValidating(true);
      authStore.setAuthLoading(true);
      authStore.setAuthError(null);

      const user = await validateCurrentSession();

      if (user) {
        authStore.setUser(user);
        authStore.setLastAuthCheck(Date.now());
      } else {
        // 会话无效，清除认证状态
        authStore.clearAuth();
      }
    } catch (error) {
      console.error('Session validation error:', error);
      const { type, message } = handleAuthError(error);

      if (type === AuthErrorType.SESSION_EXPIRED || type === AuthErrorType.AUTH_ERROR) {
        authStore.clearAuth();
        authStore.setAuthError(null); // 不显示错误，只是静默登出
      } else {
        authStore.setAuthError(message);
      }
    } finally {
      setIsValidating(false);
      authStore.setAuthLoading(false);
    }
  }, [authStore, isValidating]);

  // 定期验证会话（每 5 分钟）
  useEffect(() => {
    if (authCheckTimer.current) {
      clearInterval(authCheckTimer.current);
    }

    if (authStore.isAuthenticated) {
      authCheckTimer.current = setInterval(
        () => {
          validateSession();
        },
        5 * 60 * 1000,
      ); // 5 分钟
    }

    return () => {
      if (authCheckTimer.current) {
        clearInterval(authCheckTimer.current);
      }
    };
  }, [authStore.isAuthenticated, validateSession]);

  // 组件挂载时验证会话
  useEffect(() => {
    if (authStore.isAuthenticated && !authStore.lastAuthCheck) {
      validateSession();
    }
  }, []);

  // 处理来自主线程的消息
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data?.pluginMessage) {
        const message = event.data.pluginMessage;

        switch (message.type) {
          case 'oauth-login-request':
            // 主线程确认 OAuth 登录请求
            handleLogin();
            break;

          case 'oauth-callback':
            // 处理 OAuth 回调
            if (message.success) {
              // 这里可以处理 OAuth 回调逻辑
              validateSession();
            } else {
              authStore.setAuthError(message.error || 'OAuth 登录失败');
              authStore.setAuthLoading(false);
            }
            break;

          case 'oauth-success':
            // OAuth 登录成功
            authStore.setUser(message.user);
            authStore.setLastAuthCheck(Date.now());
            authStore.setAuthLoading(false);
            break;

          case 'oauth-error':
            // OAuth 登录错误
            authStore.setAuthError(message.error);
            authStore.setAuthLoading(false);
            break;

          case 'open-external-url':
            // 打开外部 URL
            window.open(message.url, '_blank');
            break;
        }
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [handleLogin, authStore, validateSession]);

  return {
    // 认证状态
    isAuthenticated: authStore.isAuthenticated,
    user: authStore.user,
    isLoading: authStore.isAuthLoading || isValidating,
    error: authStore.authError,
    isOAuthWindowOpen: oauthWindow !== null,

    // 权限状态
    isAdmin: authStore.isAdmin,
    isMember: authStore.isMember,

    // 用户信息
    displayName: authStore.displayName,
    avatarUrl: authStore.avatarUrl,

    // 操作方法
    login: handleLogin,
    logout: handleLogout,
    validateSession,
    clearError: () => authStore.setAuthError(null),

    // 会话信息
    lastAuthCheck: authStore.lastAuthCheck,
  };
};

// 权限检查 Hook
export const usePermission = (permission: 'admin' | 'member') => {
  const { user, isAuthenticated } = useAuth();

  const hasPermission = useCallback(() => {
    if (!isAuthenticated || !user) return false;

    if (permission === 'admin') {
      return user.role === 'admin';
    }

    if (permission === 'member') {
      return user.isMember === 1 || user.role === 'admin';
    }

    return false;
  }, [user, isAuthenticated, permission]);

  return hasPermission();
};

// 用户信息 Hook
export const useUserInfo = () => {
  const { user, isAuthenticated } = useAuth();

  return {
    user,
    isAuthenticated,
    displayName: user?.name || user?.email || 'Unknown User',
    avatarUrl: user?.figmaImageUrl,
    email: user?.email,
    role: user?.role,
    isMember: user?.isMember === 1,
    searchCount: user?.searchCount || 0,
    createdAt: user?.createdAt ? new Date(user.createdAt * 1000) : null,
  };
};
