# Repository Guidelines

## Project Structure & Module Organization
Source code lives under `src/`. `src/main.ts` wires Figma commands, while `src/ui.tsx` bootstraps the React UI. UI primitives stay in `src/components/`, routed screens in `src/pages/`, data helpers in `src/services/` and `src/utils/`, and state in `src/store/`. Keep static assets inside `src/assets/`. Plugma emits compiled bundles and the manifest into `dist/`; regenerate outputs instead of editing them. Workspace config files (Biome, Vite, TypeScript) sit at the project root.

## Build, Test, and Development Commands
Run `pnpm install` once (pnpm v10 is pinned). Use `pnpm dev` for Plugma's live Figma development loop and `pnpm preview` for the browser sandbox. Ship builds come from `pnpm build`; `pnpm release` packages the artifact for Figma Community uploads. Quality gates are `pnpm lint`, `pnpm format`, and `pnpm typecheck`; run all three before sharing work.

## Coding Style & Naming Conventions
Biome enforces two-space indentation, LF endings, 100-character lines, single quotes, and required semicolons. Keep React components in `.tsx` files and prefer arrow functions (`const WidgetPanel: FC = () => { ... };`). Follow camelCase for variables/functions and PascalCase for components, classes, types, and enums. Share styling through Tailwind utility classes or common helpers instead of custom inline CSS.

## Testing Guidelines
No dedicated test runner ships today. When adding automated coverage, colocate specs beside implementations (e.g., `src/components/Button/Button.test.tsx`) and use Vitest to align with Vite. Until then, validate changes by running `pnpm dev`, exercising flows inside the Figma desktop app, and capturing manual steps for reviewers. Use `pnpm preview` to smoke-test UI-only updates in the browser.

## Commit & Pull Request Guidelines
Commits follow Conventional Commit syntax such as `feat(store): add selection cache` or `fix(services): guard null settings`. Keep changes focused and ensure lint, format, typecheck, and build commands pass locally. Pull requests should explain the problem, solution, and verification steps, link any tracking issues, and include screenshots or GIFs for UI work.

## Configuration & Security Tips
Update plugin metadata and network rules through the `plugma.manifest` section in `package.json`. Only call services granted under `networkAccess`, routing sensitive traffic through approved proxies. Before publishing, rerun `pnpm build` so the manifest references the production bundle rather than the dev server.
